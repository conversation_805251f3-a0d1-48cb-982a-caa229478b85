import { initializeApp, getApps, getApp } from 'firebase/app';
import { getAuth, signInWithCredential, GoogleAuthProvider, User as FirebaseUser } from 'firebase/auth';
import { debugLog } from '../config/env';

// Firebase configuration from GoogleService-Info.plist
const firebaseConfig = {
  apiKey: "AIzaSyD0-cYCFQQtrDB9VzRcXHtqqFTfgd9JXWI",
  authDomain: "think-wallet-prod.firebaseapp.com",
  projectId: "think-wallet-prod",
  storageBucket: "think-wallet-prod.firebasestorage.app",
  messagingSenderId: "450493643192",
  appId: "1:450493643192:ios:243f0a77aa96f43988374d"
};

export interface FirebaseAuthResult {
  firebaseUser: FirebaseUser;
  firebaseIdToken: string;
}

export class FirebaseService {
  private static instance: FirebaseService;
  private app: any;
  private auth: any;

  public static getInstance(): FirebaseService {
    if (!FirebaseService.instance) {
      FirebaseService.instance = new FirebaseService();
    }
    return FirebaseService.instance;
  }

  constructor() {
    this.initializeFirebase();
  }

  private initializeFirebase() {
    try {
      // Initialize Firebase app if not already initialized
      if (getApps().length === 0) {
        this.app = initializeApp(firebaseConfig);
        debugLog('Firebase app initialized');
      } else {
        this.app = getApp();
        debugLog('Using existing Firebase app');
      }

      this.auth = getAuth(this.app);
      debugLog('Firebase auth initialized');
    } catch (error) {
      debugLog('Firebase initialization error:', error);
      throw error;
    }
  }

  async signInWithGoogleCredential(googleIdToken: string): Promise<FirebaseAuthResult> {
    try {
      debugLog('Signing in to Firebase with Google credential');

      // Create a Google credential with the token from Google Sign-In
      const credential = GoogleAuthProvider.credential(googleIdToken);

      // Sign in to Firebase with the Google credential
      const userCredential = await signInWithCredential(this.auth, credential);
      const firebaseUser = userCredential.user;

      // Get the Firebase ID token
      const firebaseIdToken = await firebaseUser.getIdToken();

      debugLog('Firebase sign-in successful, got Firebase ID token');

      return {
        firebaseUser,
        firebaseIdToken,
      };
    } catch (error) {
      debugLog('Firebase sign-in error:', error);
      throw error;
    }
  }

  async signOut(): Promise<void> {
    try {
      await this.auth.signOut();
      debugLog('Firebase sign out successful');
    } catch (error) {
      debugLog('Firebase sign out error:', error);
      throw error;
    }
  }

  getCurrentUser(): FirebaseUser | null {
    return this.auth.currentUser;
  }

  async getCurrentUserIdToken(): Promise<string | null> {
    try {
      const user = this.getCurrentUser();
      if (!user) return null;

      return await user.getIdToken();
    } catch (error) {
      debugLog('Error getting current user ID token:', error);
      return null;
    }
  }
}

export const firebaseService = FirebaseService.getInstance();
