import { API_ENDPOINTS, debugLog } from '../config/env';
import { storageService, AuthTokens } from './storageService';
import { GoogleUser } from './googleAuth';
import { User } from '../types/User';
import { Category } from '../types/Category';
import { TransactionType, TransactionStatus } from '../types/Transaction';

export interface GoogleAuthRequest {
  id_token: string;
}

export interface GoogleAuthResponse {
  access: string;
  refresh: string;
}

export interface RefreshTokenRequest {
  refresh: string;
}

export interface RefreshTokenResponse {
  access: string;
}

export interface ApiError {
  message: string;
  status: number;
  details?: any;
}

export interface TransactionCreateRequest {
  submit?: boolean;
  type: TransactionType;
  amount: string;
  currency: string;
  notes?: string;
  category?: string;
  receipt_uri?: string;
  recipient?: string;
  split_with?: string[];
}

export interface TransactionResponse {
  id: string;
  type: TransactionType;
  status: TransactionStatus;
  amount: number;
  currency: string;
  description?: string;
  purpose?: string;
  gl_code: string;
  program_code: string;
  receipt_uri?: string;
  recipient?: string;
  split_with?: string[];
  created_at: string;
  updated_at: string;
  created_by: string;
}

export interface LedgerStatsResponse {
  total_received: string;
  total_spent: string;
}

class ApiService {
  private static instance: ApiService;

  public static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }
    return ApiService.instance;
  }

  private async makeRequest<T>(
    url: string,
    options: RequestInit = {}
  ): Promise<T> {
    try {
      debugLog(`API Request: ${options.method || 'GET'} ${url}`);

      // Ensure Content-Type is always set for requests with body
      const defaultHeaders: Record<string, string> = {};
      if (options.body) {
        defaultHeaders['Content-Type'] = 'application/json';
      }

      const response = await fetch(url, {
        ...options,
        headers: {
          ...defaultHeaders,
          ...options.headers,
        },
      });

      debugLog(`API Response: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const error: ApiError = {
          message: errorData.message || `HTTP ${response.status}: ${response.statusText}`,
          status: response.status,
          details: errorData,
        };
        throw error;
      }

      const data = await response.json();
      debugLog('API Response Data:', data);
      return data;
    } catch (error) {
      debugLog('API Error:', error);
      throw error;
    }
  }

  private async makeAuthenticatedRequest<T>(
    url: string,
    options: RequestInit = {}
  ): Promise<T> {
    const tokens = await storageService.getTokens();

    console.log('makeAuthenticatedRequest Tokens:', tokens);

    if (!tokens) {
      throw new Error('No authentication tokens found');
    }

    // Ensure Content-Type is set for authenticated requests with body
    const authHeaders: Record<string, string> = {
      Authorization: `${tokens.accessToken}`,
    };

    if (options.body) {
      authHeaders['Content-Type'] = 'application/json';
    }

    return this.makeRequest<T>(url, {
      ...options,
      headers: {
        ...authHeaders,
        ...options.headers,
      },
    });
  }

  // Google Authentication
  async authenticateWithGoogle(googleUser: GoogleUser, tokens: any): Promise<GoogleAuthResponse> {
    try {
      debugLog('Authenticating with backend using Firebase ID token');

      const requestData: GoogleAuthRequest = {
        id_token: tokens.idToken,
      };

      debugLog('Sending Firebase ID token to backend:', {
        id_token: requestData.id_token ? requestData.id_token : 'missing',
        user_email: googleUser.email
      });

      const response = await this.makeRequest<GoogleAuthResponse>(
        API_ENDPOINTS.GOOGLE_AUTH,
        {
          method: 'POST',
          body: JSON.stringify(requestData),
        }
      );

      debugLog('Backend authentication response received');
      return response;
    } catch (error) {
      debugLog('Backend authentication error:', error);
      throw error;
    }
  }

  // Refresh Token
  async refreshAccessToken(refreshToken: string): Promise<RefreshTokenResponse> {
    try {
      debugLog('Refreshing access token');

      const requestData: RefreshTokenRequest = {
        refresh: refreshToken,
      };

      const response = await this.makeRequest<RefreshTokenResponse>(
        API_ENDPOINTS.REFRESH_TOKEN,
        {
          method: 'POST',
          body: JSON.stringify(requestData),
        }
      );

      return response;
    } catch (error) {
      debugLog('Token refresh error:', error);
      throw error;
    }
  }

  // Auto-refresh token if needed
  async ensureValidToken(): Promise<boolean> {
    try {
      const tokens = await storageService.getTokens();
      if (!tokens) {
        return false;
      }

      // Try to refresh the token
      const refreshResponse = await this.refreshAccessToken(tokens.refreshToken);
      
      // Store new tokens
      await storageService.storeTokens({
        accessToken: refreshResponse.access,
        refreshToken: tokens.refreshToken, // Keep the same refresh token
      });

      return true;
    } catch (error) {
      debugLog('Token refresh failed:', error);
      // Clear invalid tokens
      await storageService.clearTokens();
      return false;
    }
  }



  // Categories API
  async getCategories(): Promise<Category[]> {
    try {
      debugLog('Fetching categories from API');

      const response = await this.makeRequest<Category[]>(
        API_ENDPOINTS.CATEGORIES,
        {
          method: 'GET',
        }
      );

      return response;
    } catch (error) {
      debugLog('Categories fetch error:', error);
      throw error;
    }
  }

  // Users API with optional student filter
  async getUsers(onlyStudents: boolean = false): Promise<User[]> {
    try {
      debugLog(`Fetching ${onlyStudents ? 'students' : 'all users'} from API`);

      const url = onlyStudents
        ? `${API_ENDPOINTS.USERS}?only_student=true`
        : API_ENDPOINTS.USERS;

      const response = await this.makeAuthenticatedRequest<User[]>(
        url,
        {
          method: 'GET',
        }
      );

      return response;
    } catch (error) {
      debugLog('Users fetch error:', error);
      throw error;
    }
  }

  // Legacy method for backward compatibility
  async getStudents(): Promise<User[]> {
    return this.getUsers(true);
  }

  // All Users API (for recipient selection)
  async getAllUsers(): Promise<User[]> {
    return this.getUsers(false);
  }

  // Transactions API
  async createTransaction(transactionData: TransactionCreateRequest): Promise<TransactionResponse> {
    try {
      debugLog('Creating transaction via API', JSON.stringify(transactionData));

      const response = await this.makeAuthenticatedRequest<TransactionResponse>(
        API_ENDPOINTS.TRANSACTIONS,
        {
          method: 'POST',
          body: JSON.stringify(transactionData),
        }
      );

      return response;
    } catch (error) {
      debugLog('Transaction creation error:', error);
      throw error;
    }
  }

  // Ledger Stats API
  async getLedgerStats(): Promise<LedgerStatsResponse> {
    try {
      debugLog('Fetching ledger stats from API');

      const response = await this.makeAuthenticatedRequest<LedgerStatsResponse>(
        API_ENDPOINTS.LEDGER_STATS,
        {
          method: 'GET',
        }
      );

      return response;
    } catch (error) {
      debugLog('Ledger stats fetch error:', error);
      throw error;
    }
  }
}

export const apiService = ApiService.getInstance();
